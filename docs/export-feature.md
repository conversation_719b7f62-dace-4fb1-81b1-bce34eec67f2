# CSV Export Feature Documentation

## Overview

The CSV Export feature provides a simple, lightweight solution for exporting order data. It generates standard CSV files that can be opened in any spreadsheet application, text editor, or imported into other systems. This feature is perfect for users who need quick data export without complex formatting.

## Key Features

### 1. **Multiple Header Formats**
- **English Headers**: Standard CSV with English column headers for universal compatibility
- **Arabic Headers**: CSV with Arabic column headers for local use
- **UTF-8 with BOM**: Proper encoding for Excel compatibility and Arabic text support

### 2. **Export Types**
- **Selective Export**: Export specific orders by their IDs
- **Bulk Export**: Export orders with advanced filtering capabilities
- **Preview Mode**: Preview what will be exported before generating the file

### 3. **Advantages**
- **Small file size**: Lightweight format, much smaller than Excel files
- **Universal compatibility**: Opens in any text editor or spreadsheet application
- **Fast generation**: No template processing required
- **Simple format**: Easy to parse programmatically
- **Arabic support**: Full UTF-8 support with proper BOM encoding

## API Endpoints

### 1. Selective Export
**Endpoint**: `POST /orders/export/selective`

**Request Body**:
```json
{
    "export_type": "selective",
    "order_ids": [1, 2, 3, 4, 5],
    "filename": "selected_orders.csv"
}
```

**Response**: CSV file download with English headers

### 2. Bulk Export (English Headers)
**Endpoint**: `POST /orders/export/bulk`

**Request Body**:
```json
{
    "export_type": "bulk",
    "filters": {
        "created_date_range": {
            "start_date": "2025-06-01",
            "end_date": "2025-06-20"
        },
        "status": ["PENDING", "COMPLETED"],
        "min_total_price": 100.0
    },
    "filename": "bulk_export.csv",
    "limit": 1000
}
```

**Response**: CSV file download with English headers

### 3. Bulk Export (Arabic Headers)
**Endpoint**: `POST /orders/export/bulk-arabic`

**Request Body**: Same as bulk export

**Response**: CSV file download with Arabic headers

### 4. Export Preview
**Endpoint**: `POST /orders/export/preview`

**Request Body**: Same as bulk export

**Response**:
```json
{
    "success": true,
    "total_orders": 150,
    "sample_orders": [
        {
            "id": 1,
            "code": "ORD-001",
            "customer_name": "أحمد محمد",
            "customer_phone": "01234567890",
            "company": "شركة ABC",
            "total_price": 250.0,
            "status": "COMPLETED",
            "created_at": "2025-06-20T10:30:00Z"
        }
    ],
    "estimated_file_size_kb": 30.5,
    "filters_applied": {
        "status": ["COMPLETED"],
        "min_total_price": 100.0
    }
}
```

## CSV Format Structure

### English Headers Format
```csv
"Order ID","Order Code","Customer Name","Customer Phone","Customer Address","Company","Total Price","Status","Assigned To","Created Date","Updated Date","Notes"
"1","ORD-001","أحمد محمد","01234567890","القاهرة","شركة ABC","250.0","COMPLETED","أحمد علي","2025-06-20 10:30:00","2025-06-20 15:45:00","ملاحظات الطلب"
```

### Arabic Headers Format
```csv
"رقم الطلب","كود الطلب","اسم العميل","رقم الهاتف","العنوان","الشركة","السعر الإجمالي","الحالة","مُعين إلى","تاريخ الإنشاء","تاريخ التحديث","ملاحظات"
"1","ORD-001","أحمد محمد","01234567890","القاهرة","شركة ABC","250.0","COMPLETED","أحمد علي","2025-06-20 10:30:00","2025-06-20 15:45:00","ملاحظات الطلب"
```

## Column Mapping

| Column | English Header | Arabic Header | Order Field | Data Type |
|--------|----------------|---------------|-------------|-----------|
| 1 | Order ID | رقم الطلب | id | Integer |
| 2 | Order Code | كود الطلب | code | String |
| 3 | Customer Name | اسم العميل | customer_name | String |
| 4 | Customer Phone | رقم الهاتف | customer_phone | String |
| 5 | Customer Address | العنوان | customer_address | String |
| 6 | Company | الشركة | customer_company.name | String |
| 7 | Total Price | السعر الإجمالي | total_price | Float |
| 8 | Status | الحالة | order_handling_status | String |
| 9 | Assigned To | مُعين إلى | assigned_to.get_full_name() | String |
| 10 | Created Date | تاريخ الإنشاء | created_at | DateTime |
| 11 | Updated Date | تاريخ التحديث | updated_at | DateTime |
| 12 | Notes | ملاحظات | notes | String |

## Advanced Filtering

### Date Range Filters
```json
{
    "created_date_range": {
        "start_date": "2025-06-01",
        "end_date": "2025-06-20"
    },
    "updated_date_range": {
        "start_date": "2025-06-15",
        "end_date": "2025-06-20"
    }
}
```

### Status Filters
```json
{
    "status": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"]
}
```

### User Filters
```json
{
    "assigned_to_ids": [1, 2, 3]
}
```

### Company Filters
```json
{
    "company_ids": [1, 2, 3],
    "company_codes": ["ABC", "XYZ", "DEF"]
}
```

### Customer Filters
```json
{
    "customer_name_contains": "أحمد",
    "customer_phone_contains": "0123"
}
```

### Price Filters
```json
{
    "min_total_price": 100.0,
    "max_total_price": 1000.0
}
```

### Additional Filters
```json
{
    "office_ids": [1, 2],
    "has_notes": true,
    "limit": 1000
}
```

## Usage Examples

### JavaScript/Frontend Integration

```javascript
// Selective export
const exportSelected = async (orderIds) => {
    const response = await fetch('/api/orders/export/selective', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_type: 'selective',
            order_ids: orderIds,
            filename: 'selected_orders.csv'
        })
    });
    
    if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'selected_orders.csv';
        a.click();
    }
};

// Bulk export with English headers
const exportBulk = async (filters) => {
    const response = await fetch('/api/orders/export/bulk', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_type: 'bulk',
            filters: filters,
            limit: 1000
        })
    });
    
    if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'bulk_export.csv';
        a.click();
    }
};

// Bulk export with Arabic headers
const exportBulkArabic = async (filters) => {
    const response = await fetch('/api/orders/export/bulk-arabic', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_type: 'bulk',
            filters: filters,
            limit: 1000
        })
    });
    
    if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'bulk_export_arabic.csv';
        a.click();
    }
};

// Preview before export
const previewExport = async (filters) => {
    const response = await fetch('/api/orders/export/preview', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            export_type: 'bulk',
            filters: filters
        })
    });
    
    const result = await response.json();
    console.log(`Will export ${result.total_orders} orders`);
    console.log(`Estimated size: ${result.estimated_file_size_kb} KB`);
    
    return result;
};
```

### Python Integration

```python
import requests

class OrderExportClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def export_selective(self, order_ids, filename=None):
        """Export specific orders to CSV."""
        url = f"{self.base_url}/orders/export/selective"
        data = {
            'export_type': 'selective',
            'order_ids': order_ids,
            'filename': filename
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.ok:
            filename = filename or f'export_{len(order_ids)}_orders.csv'
            with open(filename, 'wb') as f:
                f.write(response.content)
            return filename
        else:
            raise Exception(f"Export failed: {response.text}")
    
    def export_bulk(self, filters, filename=None, limit=None, arabic_headers=False):
        """Export orders with filters to CSV."""
        endpoint = 'bulk-arabic' if arabic_headers else 'bulk'
        url = f"{self.base_url}/orders/export/{endpoint}"
        
        data = {
            'export_type': 'bulk',
            'filters': filters,
            'filename': filename,
            'limit': limit
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.ok:
            filename = filename or f'bulk_export{"_arabic" if arabic_headers else ""}.csv'
            with open(filename, 'wb') as f:
                f.write(response.content)
            return filename
        else:
            raise Exception(f"Export failed: {response.text}")
    
    def preview_export(self, filters):
        """Preview export results."""
        url = f"{self.base_url}/orders/export/preview"
        data = {
            'export_type': 'bulk',
            'filters': filters
        }
        
        response = requests.post(url, headers=self.headers, json=data)
        return response.json()

# Usage
client = OrderExportClient('https://api.example.com', 'your-token')

# Export specific orders
client.export_selective([1, 2, 3, 4, 5], 'my_orders.csv')

# Export with filters (English headers)
filters = {
    'status': ['COMPLETED'],
    'min_total_price': 100.0
}

# Preview first
preview = client.preview_export(filters)
print(f"Will export {preview['total_orders']} orders")

# Then export
client.export_bulk(filters, 'filtered_orders.csv', limit=1000)

# Export with Arabic headers
client.export_bulk(filters, 'filtered_orders_arabic.csv', limit=1000, arabic_headers=True)
```

## Technical Details

### Encoding
- **UTF-8 with BOM**: Ensures proper display of Arabic text in Excel
- **CSV quoting**: All fields are quoted to handle commas and special characters
- **Line endings**: Standard CRLF for Windows compatibility

### Performance
- **Memory efficient**: Streams data directly to response
- **Fast generation**: No template processing overhead
- **Small files**: Minimal overhead, approximately 0.2KB per order

### Security
- **Same permissions**: Uses identical access control as other endpoints
- **Office isolation**: Users can only export orders from their office
- **Rate limiting**: Standard rate limits apply
- **Input validation**: Comprehensive validation of all request parameters

---

*The CSV export feature provides a fast, lightweight solution for order data export with full Arabic support and comprehensive filtering capabilities.*
