"""
URL configuration for core project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.forms import ValidationError
from django.urls import path
from ninja import NinjaAPI

from accounts.api import accountsApi
from core.api_docs import register_docs_endpoints
from orders.api import orderApi, companiesApi, companyChannelsApi
from offices.api import router as officesApi
from orders.bulk_import_api import bulkImportApi
from core.errors import HttpError

api = NinjaAPI()

register_docs_endpoints(api)


api.add_router("/accounts/", accountsApi, tags=["accounts"])
api.add_router("/", orderApi, tags=["orders"])
api.add_router("/", companiesApi, tags=["companies"])
api.add_router("/", companyChannelsApi, tags=["company-channels"])
api.add_router("/", officesApi, tags=["offices"])
api.add_router("/orders-bulk", bulkImportApi, tags=["orders-bulk"])


urlpatterns = [path("admin/", admin.site.urls), path("api/", api.urls)]


@api.exception_handler(HttpError)
def http_error(request, exc):
    return api.create_response(
        request,
        status=exc.status_code,
        data={"status": "error", "code": exc.code, "message": exc.message},
    )


@api.exception_handler(ValidationError)
def validation_error(request, exc):
    return api.create_response(
        request,
        status=400,
        data={
            "status": "error",
            "code": "validation_error",
            "message": exc.message,
        },
    )
