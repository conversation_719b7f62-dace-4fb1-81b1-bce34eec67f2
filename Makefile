

dev:
	uv run python manage.py runserver 0.0.0.0:8000

migrate:
	uv run python manage.py migrate


migrations:
	uv run python manage.py makemigrations

test:
	uv run pytest

clean:
	find . -name "*.pyc" -delete
	find . -name "*.pyo" -delete
	find . -name "*.pyd" -delete
	find . -name "__pycache__" -delete
	find . -name "*.sqlite3" -delete
	find . -name "*.sqlite3-journal" -delete
	find . -name "*.sqlite3-shm" -delete
	find . -name "*.sqlite3-wal" -delete

