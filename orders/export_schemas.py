"""
Export schemas for order CSV export functionality.
Simplified version focusing only on CSV export.
"""

from datetime import date
from enum import Enum
from typing import List, Optional, Union
from pydantic import BaseModel, Field, validator
from django.db.models import QuerySet, Q


class ExportType(str, Enum):
    """Export type enumeration."""

    SELECTIVE = "selective"
    BULK = "bulk"


class OrderStatus(str, Enum):
    """Order status enumeration for filtering."""

    PENDING = "PENDING"
    ASSIGNED = "ASSIGNED"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"


class DateRangeFilter(BaseModel):
    """Date range filter for orders."""

    start_date: Optional[date] = Field(None, description="Start date (inclusive)")
    end_date: Optional[date] = Field(None, description="End date (inclusive)")

    @validator("end_date")
    def end_date_after_start_date(cls, v, values):
        if v and values.get("start_date") and v < values["start_date"]:
            raise ValueError("end_date must be after start_date")
        return v


class OrderExportFilters(BaseModel):
    """Filters for bulk order export."""

    # Date filters
    created_date_range: Optional[DateRangeFilter] = Field(
        None, description="Filter by creation date range"
    )
    updated_date_range: Optional[DateRangeFilter] = Field(
        None, description="Filter by update date range"
    )

    # Status filters
    status: Optional[List[OrderStatus]] = Field(
        None, description="Filter by order status"
    )

    # User/Representative filters
    assigned_to_ids: Optional[List[int]] = Field(
        None, description="Filter by assigned user IDs"
    )

    # Company filters
    company_ids: Optional[List[int]] = Field(None, description="Filter by company IDs")
    company_codes: Optional[List[str]] = Field(
        None, description="Filter by company codes"
    )

    # Customer filters
    customer_name_contains: Optional[str] = Field(
        None, description="Filter by customer name (partial match)"
    )
    customer_phone_contains: Optional[str] = Field(
        None, description="Filter by customer phone (partial match)"
    )

    # Price filters
    min_total_price: Optional[float] = Field(
        None, description="Minimum total price filter", ge=0
    )
    max_total_price: Optional[float] = Field(
        None, description="Maximum total price filter", ge=0
    )

    # Office filters
    office_ids: Optional[List[int]] = Field(None, description="Filter by office IDs")

    # Additional filters
    has_notes: Optional[bool] = Field(None, description="Filter orders that have notes")

    @validator("max_total_price")
    def max_price_greater_than_min(cls, v, values):
        if v and values.get("min_total_price") and v < values["min_total_price"]:
            raise ValueError("max_total_price must be greater than min_total_price")
        return v


class SelectiveExportRequest(BaseModel):
    """Request schema for selective order export."""

    export_type: ExportType = Field(ExportType.SELECTIVE, description="Export type")
    order_ids: List[int] = Field(
        ..., description="List of order IDs to export", min_items=1
    )
    filename: Optional[str] = Field(None, description="Custom filename for export")
    export_date: Optional[date] = Field(
        default_factory=date.today, description="Export date"
    )

    @validator("order_ids")
    def validate_order_ids(cls, v):
        if not v:
            raise ValueError("At least one order ID is required")
        if len(v) > 1000:
            raise ValueError("Cannot export more than 1000 orders at once")
        return v

    @validator("filename")
    def validate_filename(cls, v):
        if v:
            # Remove any path separators for security
            v = v.replace("/", "").replace("\\", "").replace("..", "")
            if not v.endswith(".csv"):
                v = v.replace(".xlsx", ".csv").replace(".xls", ".csv")
                if not v.endswith(".csv"):
                    v += ".csv"
        return v


class BulkExportRequest(BaseModel):
    """Request schema for bulk order export with filters."""

    export_type: ExportType = Field(ExportType.BULK, description="Export type")
    filters: OrderExportFilters = Field(
        default_factory=OrderExportFilters, description="Export filters"
    )
    filename: Optional[str] = Field(None, description="Custom filename for export")
    limit: Optional[int] = Field(
        None, description="Maximum number of orders to export", gt=0, le=50000
    )
    export_date: Optional[date] = Field(
        default_factory=date.today, description="Export date"
    )

    @validator("filename")
    def validate_filename(cls, v):
        if v:
            # Remove any path separators for security
            v = v.replace("/", "").replace("\\", "").replace("..", "")
            if not v.endswith(".csv"):
                v = v.replace(".xlsx", ".csv").replace(".xls", ".csv")
                if not v.endswith(".csv"):
                    v += ".csv"
        return v


class ExportResult(BaseModel):
    """Result schema for export operations."""

    success: bool = Field(..., description="Whether the export was successful")
    total_orders: int = Field(..., description="Total number of orders exported")
    file_size_bytes: Optional[int] = Field(
        None, description="Size of exported file in bytes"
    )
    filename: str = Field(..., description="Name of the exported file")
    export_date: date = Field(..., description="Date when export was performed")


class ExportError(BaseModel):
    """Error schema for export operations."""

    success: bool = Field(False, description="Always false for errors")
    error_type: str = Field(..., description="Type of error")
    error_code: str = Field(..., description="Error code")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[dict] = Field(None, description="Additional error details")


def apply_filters_to_queryset(
    queryset: QuerySet, filters: OrderExportFilters
) -> QuerySet:
    """
    Apply export filters to a Django QuerySet.

    Args:
        queryset: Base QuerySet to filter
        filters: OrderExportFilters object with filter criteria

    Returns:
        Filtered QuerySet
    """
    # Date range filters
    if filters.created_date_range:
        if filters.created_date_range.start_date:
            queryset = queryset.filter(
                created_at__date__gte=filters.created_date_range.start_date
            )
        if filters.created_date_range.end_date:
            queryset = queryset.filter(
                created_at__date__lte=filters.created_date_range.end_date
            )

    if filters.updated_date_range:
        if filters.updated_date_range.start_date:
            queryset = queryset.filter(
                updated_at__date__gte=filters.updated_date_range.start_date
            )
        if filters.updated_date_range.end_date:
            queryset = queryset.filter(
                updated_at__date__lte=filters.updated_date_range.end_date
            )

    # Status filters
    if filters.status:
        queryset = queryset.filter(order_handling_status__in=filters.status)

    # User filters
    if filters.assigned_to_ids:
        queryset = queryset.filter(assigned_to_id__in=filters.assigned_to_ids)

    # Company filters
    if filters.company_ids:
        queryset = queryset.filter(customer_company_id__in=filters.company_ids)

    if filters.company_codes:
        queryset = queryset.filter(customer_company__code__in=filters.company_codes)

    # Customer filters
    if filters.customer_name_contains:
        queryset = queryset.filter(
            customer_name__icontains=filters.customer_name_contains
        )

    if filters.customer_phone_contains:
        queryset = queryset.filter(
            customer_phone__icontains=filters.customer_phone_contains
        )

    # Price filters
    if filters.min_total_price is not None:
        queryset = queryset.filter(total_price__gte=filters.min_total_price)

    if filters.max_total_price is not None:
        queryset = queryset.filter(total_price__lte=filters.max_total_price)

    # Office filters
    if filters.office_ids:
        queryset = queryset.filter(office_id__in=filters.office_ids)

    # Additional filters
    if filters.has_notes is not None:
        if filters.has_notes:
            queryset = queryset.exclude(Q(notes__isnull=True) | Q(notes__exact=""))
        else:
            queryset = queryset.filter(Q(notes__isnull=True) | Q(notes__exact=""))

    return queryset


def validate_export_request(
    request_data: dict,
) -> Union[SelectiveExportRequest, BulkExportRequest]:
    """
    Validate and parse export request data.

    Args:
        request_data: Raw request data dictionary

    Returns:
        Validated request object

    Raises:
        ValueError: If request data is invalid
    """
    export_type = request_data.get("export_type")

    if export_type == ExportType.SELECTIVE:
        return SelectiveExportRequest(**request_data)
    elif export_type == ExportType.BULK:
        return BulkExportRequest(**request_data)
    else:
        raise ValueError(f"Invalid export_type: {export_type}")
