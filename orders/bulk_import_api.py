"""
API endpoints for bulk import management functionality.
Handles OrdersBulkSheet operations including listing, viewing, and deleting bulk imports.
"""

from ninja import Router, Query
from ninja.errors import HttpError
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count
from django.db import transaction
from typing import List, Optional
from math import ceil
import logging

from accounts.models import User, Role
from accounts.auth import AuthBearer
from .models import OrdersBulkSheet, Order
from .bulk_import_schemas import (
    BulkImportListRequest,
    BulkImportListResponse,
    BulkImportDetailResponse,
    BulkImportDeleteResponse,
    BulkImportStatsResponse,
    BulkImportOrdersRequest,
    BulkImportSummarySchema,
    BulkImportDetailSchema,
    BulkImportWithOrdersSchema,
    OrderSummarySchema,
    BulkImportStatsSchema,
)

logger = logging.getLogger(__name__)

bulkImportApi = Router()


@bulkImportApi.get("/", auth=AuthBearer(), response=BulkImportListResponse)
def list_bulk_imports(request, filters: BulkImportListRequest = Query(...)):
    """
    List all bulk import sessions for the user's office with filtering and pagination.

    Args:
        filters: Filtering and pagination parameters

    Returns:
        Paginated list of bulk import sessions
    """
    user = request.auth

    # Check permissions
    if user.role not in [Role.MASTER, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpError(
            403,
            "Forbidden: You don't have permission to view bulk imports.",
            "FORBIDDEN",
        )

    try:
        # Start with base queryset
        queryset = (
            OrdersBulkSheet.objects.filter(office=user.office)
            .select_related("imported_by")
            .annotate(orders_count=Count("order"))
        )

        # Apply filters
        if filters.search:
            queryset = queryset.filter(
                Q(name__icontains=filters.search)
                | Q(original_filename__icontains=filters.search)
            )

        if filters.imported_by_id:
            queryset = queryset.filter(imported_by_id=filters.imported_by_id)

        if filters.date_from:
            queryset = queryset.filter(created_at__gte=filters.date_from)

        if filters.date_to:
            queryset = queryset.filter(created_at__lte=filters.date_to)

        if filters.has_orders is not None:
            if filters.has_orders:
                queryset = queryset.filter(orders_count__gt=0)
            else:
                queryset = queryset.filter(orders_count=0)

        total_count = queryset.count()

        # Apply pagination
        start_idx = (filters.page - 1) * filters.page_size
        paginated_items = queryset[start_idx : start_idx + filters.page_size]

        # Convert to schema
        bulk_imports = []
        for item in paginated_items:
            bulk_imports.append(
                BulkImportSummarySchema(
                    id=item.id,
                    name=item.name,
                    original_filename=item.original_filename,
                    imported_by_username=item.imported_by.username,
                    imported_by_full_name=item.imported_by.get_full_name()
                    or item.imported_by.username,
                    total_rows_processed=item.total_rows_processed,
                    successful_imports=item.successful_imports,
                    failed_imports=item.failed_imports,
                    skipped_rows=item.skipped_rows,
                    success_rate=item.success_rate,
                    orders_count=getattr(item, "orders_count", 0),
                    created_at=item.created_at,
                    updated_at=item.updated_at,
                )
            )

        total_pages = ceil(total_count / filters.page_size) if total_count > 0 else 1

        return BulkImportListResponse(
            total_count=total_count,
            bulk_imports=bulk_imports,
            page=filters.page,
            page_size=filters.page_size,
            total_pages=total_pages,
        )

    except Exception as e:
        logger.error(f"Error listing bulk imports: {str(e)}")
        raise HttpError(
            500, f"Failed to list bulk imports: {str(e)}", "INTERNAL_SERVER_ERROR"
        )


@bulkImportApi.get(
    "/{bulk_import_id}", auth=AuthBearer(), response=BulkImportDetailResponse
)
def get_bulk_import_detail(request, bulk_import_id: int):
    """
    Get detailed information about a specific bulk import session.

    Args:
        bulk_import_id: ID of the bulk import session

    Returns:
        Detailed bulk import information
    """
    user = request.auth

    # Check permissions
    if user.role not in [Role.MASTER, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpError(
            403,
            "Forbidden: You don't have permission to view bulk imports.",
            "FORBIDDEN",
        )

    try:
        # Get bulk import with related data
        bulk_sheet = get_object_or_404(
            OrdersBulkSheet.objects.select_related("imported_by").annotate(
                orders_count=Count("order")
            ),
            id=bulk_import_id,
            office=user.office,
        )

        # Get associated orders
        orders = (
            Order.objects.filter(orders_bulk_sheet=bulk_sheet)
            .select_related("customer_company", "assigned_to", "order_delivery_status")
            .order_by("-created_at")
        )

        # Convert orders to schema
        order_schemas = []
        for order in orders:
            order_schemas.append(
                OrderSummarySchema(
                    id=order.id,
                    code=order.code,
                    customer_name=order.customer_name,
                    customer_phone=order.customer_phone,
                    customer_address=order.customer_address,
                    total_price=float(order.total_price) if order.total_price else None,
                    customer_company_name=order.customer_company.name
                    if order.customer_company
                    else None,
                    customer_company_code=order.customer_company.code
                    if order.customer_company
                    else None,
                    order_handling_status=order.order_handling_status,
                    order_delivery_status_name=order.order_delivery_status.name
                    if order.order_delivery_status
                    else None,
                    assigned_to_username=order.assigned_to.username
                    if order.assigned_to
                    else None,
                    assigned_at=order.assigned_at,
                    created_at=order.created_at,
                    updated_at=order.updated_at,
                )
            )

        # Create detailed schema
        bulk_import_detail = BulkImportWithOrdersSchema(
            id=bulk_sheet.id,
            name=bulk_sheet.name,
            original_filename=bulk_sheet.original_filename,
            imported_by_username=bulk_sheet.imported_by.username,
            imported_by_full_name=bulk_sheet.imported_by.get_full_name()
            or bulk_sheet.imported_by.username,
            total_rows_processed=bulk_sheet.total_rows_processed,
            successful_imports=bulk_sheet.successful_imports,
            failed_imports=bulk_sheet.failed_imports,
            skipped_rows=bulk_sheet.skipped_rows,
            success_rate=bulk_sheet.success_rate,
            orders_count=getattr(bulk_sheet, "orders_count", len(order_schemas)),
            created_at=bulk_sheet.created_at,
            updated_at=bulk_sheet.updated_at,
            sheet_file_url=bulk_sheet.sheet_file.url if bulk_sheet.sheet_file else None,
            import_config=bulk_sheet.import_config,
            sheet_id=bulk_sheet.sheet_id,
            sheet_name=bulk_sheet.sheet_name,
            orders=order_schemas,
        )

        return BulkImportDetailResponse(bulk_import=bulk_import_detail)

    except Exception as e:
        logger.error(f"Error getting bulk import detail: {str(e)}")
        raise HttpError(
            500, f"Failed to get bulk import detail: {str(e)}", "INTERNAL_SERVER_ERROR"
        )


@bulkImportApi.delete(
    "/{bulk_import_id}", auth=AuthBearer(), response=BulkImportDeleteResponse
)
def delete_bulk_import(request, bulk_import_id: int):
    """
    Delete a bulk import session and all its associated orders.

    Args:
        bulk_import_id: ID of the bulk import session to delete

    Returns:
        Deletion confirmation with statistics
    """
    user = request.auth

    # Check permissions - only Master and Manager can delete bulk imports
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403, "Forbidden: Only Master/Manager can delete bulk imports.", "FORBIDDEN"
        )

    try:
        # Get bulk import
        bulk_sheet = get_object_or_404(
            OrdersBulkSheet,
            id=bulk_import_id,
            office=user.office,
        )

        with transaction.atomic():
            # Delete using the model method which handles orders deletion
            deleted_orders_count = bulk_sheet.delete_with_orders()

            return BulkImportDeleteResponse(
                message=f"Successfully deleted bulk import '{bulk_sheet.name}' and {deleted_orders_count} associated orders",
                deleted_bulk_import_id=bulk_import_id,
                deleted_orders_count=deleted_orders_count,
            )

    except Exception as e:
        logger.error(f"Error deleting bulk import: {str(e)}")
        raise HttpError(
            500, f"Failed to delete bulk import: {str(e)}", "INTERNAL_SERVER_ERROR"
        )


@bulkImportApi.get("/stats", auth=AuthBearer(), response=BulkImportStatsResponse)
def get_bulk_import_stats(request):
    """
    Get statistics about bulk imports for the user's office.

    Returns:
        Bulk import statistics
    """
    user = request.auth

    # Check permissions
    if user.role not in [Role.MASTER, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpError(
            403,
            "Forbidden: You don't have permission to view bulk import stats.",
            "FORBIDDEN",
        )

    try:
        from django.db.models import Sum, Avg

        # Get all bulk imports for the office
        bulk_imports = OrdersBulkSheet.objects.filter(office=user.office)

        if not bulk_imports.exists():
            stats = BulkImportStatsSchema(
                total_bulk_imports=0,
                total_orders_imported=0,
                total_successful_imports=0,
                total_failed_imports=0,
                average_success_rate=0.0,
                most_recent_import=None,
            )
        else:
            # Calculate statistics
            aggregates = bulk_imports.aggregate(
                total_successful=Sum("successful_imports"),
                total_failed=Sum("failed_imports"),
                total_processed=Sum("total_rows_processed"),
            )

            total_successful = aggregates["total_successful"] or 0
            total_failed = aggregates["total_failed"] or 0
            total_processed = aggregates["total_processed"] or 0

            # Calculate average success rate
            if total_processed > 0:
                avg_success_rate = (total_successful / total_processed) * 100
            else:
                avg_success_rate = 0.0

            # Get most recent import
            most_recent = bulk_imports.order_by("-created_at").first()

            stats = BulkImportStatsSchema(
                total_bulk_imports=bulk_imports.count(),
                total_orders_imported=total_successful,
                total_successful_imports=total_successful,
                total_failed_imports=total_failed,
                average_success_rate=round(avg_success_rate, 2),
                most_recent_import=most_recent.created_at if most_recent else None,
            )

        return BulkImportStatsResponse(stats=stats)

    except Exception as e:
        logger.error(f"Error getting bulk import stats: {str(e)}")
        raise HttpError(
            500, f"Failed to get bulk import stats: {str(e)}", "INTERNAL_SERVER_ERROR"
        )
