"""
Schemas for bulk import management functionality.
Handles OrdersBulkSheet operations including listing, viewing, and deleting bulk imports.
"""

from ninja import Schema, Field
from ninja_schema import ModelSchema
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel

from .models import OrdersBulkSheet, Order


class BulkImportSummarySchema(Schema):
    """Summary information for a bulk import session."""
    
    id: int
    name: str
    original_filename: str
    imported_by_username: str = Field(..., alias="imported_by.username")
    imported_by_full_name: str = Field(..., alias="imported_by.get_full_name")
    
    # Import statistics
    total_rows_processed: int
    successful_imports: int
    failed_imports: int
    skipped_rows: int
    success_rate: float
    
    # Actual order count (may differ from successful_imports if orders were deleted)
    orders_count: int
    
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class BulkImportDetailSchema(BulkImportSummarySchema):
    """Detailed information for a bulk import session including configuration."""
    
    sheet_file_url: Optional[str] = None
    import_config: Dict[str, Any] = Field(default_factory=dict)
    
    # Legacy fields
    sheet_id: str = ""
    sheet_name: str = ""


class OrderSummarySchema(Schema):
    """Summary information for an order within a bulk import."""
    
    id: int
    code: str
    customer_name: str
    customer_phone: str
    customer_address: str
    total_price: Optional[float] = None
    
    # Company information
    customer_company_name: Optional[str] = Field(None, alias="customer_company.name")
    customer_company_code: Optional[str] = Field(None, alias="customer_company.code")
    
    # Status information
    order_handling_status: str
    order_delivery_status_name: Optional[str] = Field(None, alias="order_delivery_status.name")
    
    # Assignment information
    assigned_to_username: Optional[str] = Field(None, alias="assigned_to.username")
    assigned_at: Optional[datetime] = None
    
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class BulkImportWithOrdersSchema(BulkImportDetailSchema):
    """Bulk import with associated orders list."""
    
    orders: List[OrderSummarySchema] = Field(default_factory=list)


class BulkImportListResponse(Schema):
    """Response schema for listing bulk imports."""
    
    success: bool = True
    total_count: int
    bulk_imports: List[BulkImportSummarySchema]
    
    # Pagination info
    page: int = 1
    page_size: int = 20
    total_pages: int


class BulkImportDetailResponse(Schema):
    """Response schema for bulk import details."""
    
    success: bool = True
    bulk_import: BulkImportWithOrdersSchema


class BulkImportDeleteResponse(Schema):
    """Response schema for bulk import deletion."""
    
    success: bool = True
    message: str
    deleted_bulk_import_id: int
    deleted_orders_count: int


class BulkImportStatsSchema(Schema):
    """Statistics for bulk imports."""
    
    total_bulk_imports: int
    total_orders_imported: int
    total_successful_imports: int
    total_failed_imports: int
    average_success_rate: float
    most_recent_import: Optional[datetime] = None


class BulkImportStatsResponse(Schema):
    """Response schema for bulk import statistics."""
    
    success: bool = True
    stats: BulkImportStatsSchema


# Request schemas for filtering and pagination
class BulkImportListRequest(Schema):
    """Request schema for listing bulk imports with filters."""
    
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(20, ge=1, le=100, description="Items per page")
    
    # Filters
    search: Optional[str] = Field(None, description="Search in name or filename")
    imported_by_id: Optional[int] = Field(None, description="Filter by user who imported")
    date_from: Optional[datetime] = Field(None, description="Filter imports from this date")
    date_to: Optional[datetime] = Field(None, description="Filter imports to this date")
    min_success_rate: Optional[float] = Field(None, ge=0, le=100, description="Minimum success rate")
    has_orders: Optional[bool] = Field(None, description="Filter by whether bulk import has orders")


class BulkImportOrdersRequest(Schema):
    """Request schema for getting orders from a bulk import."""
    
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(50, ge=1, le=200, description="Items per page")
    
    # Filters for orders
    search: Optional[str] = Field(None, description="Search in customer name or phone")
    status: Optional[str] = Field(None, description="Filter by order handling status")
    assigned_to_id: Optional[int] = Field(None, description="Filter by assigned user")
