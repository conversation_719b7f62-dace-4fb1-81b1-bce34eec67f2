from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from accounts.models import User
from offices.models import Office


class OrderHandlingStatus(models.TextChoices):
    PENDING = "PENDING", _("Pending")
    ASSIGNED = "ASSIGNED", _("Assigned")
    PROCESSING = "PROCESSING", _("Processing")
    COMPLETED = "COMPLETED", _("Completed")


class Company(models.Model):
    office = models.ForeignKey(Office, on_delete=models.CASCADE)
    code = models.CharField(max_length=20, unique=True, null=True, blank=True)
    name = models.CharField(max_length=255)
    address = models.TextField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    color_code = models.CharField(
        max_length=20,
        blank=True,
        help_text="Hex color code for the company",
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "companies"
        verbose_name = _("Company")
        verbose_name_plural = _("Companies")


class CompanyChannel(models.Model):
    office = models.ForeignKey(Office, on_delete=models.CASCADE)
    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=255)
    notes = models.TextField(blank=True)
    channel_whatsapp_number = models.CharField(
        max_length=255,
        help_text=_("WhatsApp number for the channel"),
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "company_channels"
        verbose_name = _("Company Channel")
        verbose_name_plural = _("Company Channels")


class OrderDeliveryStatus(models.Model):
    office = models.ForeignKey(Office, on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    order_default_handling_status = models.CharField(
        max_length=20, choices=OrderHandlingStatus.choices, null=True, blank=True
    )
    just_delivery_commission_rate = models.BooleanField(default=False)
    percentage_commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        null=True,
        blank=True,
        help_text=_("Percentage of the order total price, null if not applicable"),
    )
    percentage_of_order_total_price = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        null=True,
        blank=True,
        help_text=_("Percentage of the order total price, null if not applicable"),
    )

    def __str__(self):
        return self.name

    class Meta:
        db_table = "order_delivery_statuses"
        verbose_name = _("Order Delivery Status")
        verbose_name_plural = _("Order Delivery Statuses")


class OrdersBulkSheet(models.Model):
    office = models.ForeignKey(Office, on_delete=models.CASCADE)
    imported_by = models.ForeignKey(
        User, on_delete=models.CASCADE, help_text=_("User who performed the import")
    )

    # File information
    name = models.CharField(max_length=255, help_text=_("Display name for the import"))
    original_filename = models.CharField(
        max_length=255, help_text=_("Original filename of the uploaded Excel file")
    )
    sheet_file = models.FileField(
        upload_to="uploads/orders_bulk_sheets/",
        null=True,
        blank=True,
        help_text=_("Stored copy of the imported Excel file"),
    )

    # Import statistics
    total_rows_processed = models.PositiveIntegerField(
        default=0, help_text=_("Total number of rows processed from Excel")
    )
    successful_imports = models.PositiveIntegerField(
        default=0, help_text=_("Number of orders successfully imported")
    )
    failed_imports = models.PositiveIntegerField(
        default=0, help_text=_("Number of rows that failed to import")
    )
    skipped_rows = models.PositiveIntegerField(
        default=0, help_text=_("Number of rows that were skipped")
    )

    # Import configuration used
    import_config = models.JSONField(
        default=dict,
        blank=True,
        help_text=_("Configuration used for the import (column mapping, etc.)"),
    )

    # Legacy fields (keeping for backward compatibility)
    sheet_id = models.CharField(max_length=255, blank=True, default="")
    sheet_name = models.CharField(max_length=255, blank=True, default="")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "orders_bulk_sheets"
        verbose_name = _("Orders Bulk Sheet")
        verbose_name_plural = _("Orders Bulk Sheets")
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.name} ({self.successful_imports} orders)"

    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_rows_processed == 0:
            return 0.0
        return (self.successful_imports / self.total_rows_processed) * 100

    @property
    def orders_count(self) -> int:
        """Get the actual count of associated orders."""
        return self.order_set.count()

    def delete_with_orders(self):
        """Delete the bulk sheet and all associated orders."""
        from django.db import transaction

        with transaction.atomic():
            # Delete all associated orders
            orders_deleted = self.order_set.count()
            self.order_set.all().delete()

            # Delete the bulk sheet itself
            self.delete()

            return orders_deleted


class Order(models.Model):
    office = models.ForeignKey(Office, on_delete=models.CASCADE)
    orders_bulk_sheet = models.ForeignKey(
        OrdersBulkSheet, on_delete=models.CASCADE, null=True, blank=True
    )
    code = models.CharField(max_length=255)
    notes = models.TextField(blank=True)
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Amount customer should pay"),
    )
    customer_name = models.CharField(max_length=255)
    customer_phone = models.TextField(
        help_text=_("List of customer phone numbers separated by `-`"),
    )
    customer_address = models.TextField(blank=True)
    customer_company = models.ForeignKey(Company, on_delete=models.CASCADE)

    special_commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(500)],
        null=True,
        blank=True,
        help_text=_("Special commission rate for the order"),
    )
    delivery_deadline_date = models.DateTimeField(null=True, blank=True)
    delivery_customer_payment = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Amount paid by the customer"),
    )
    order_handling_status = models.CharField(
        max_length=20,
        choices=OrderHandlingStatus.choices,
        default=OrderHandlingStatus.PENDING,
    )
    order_delivery_status = models.ForeignKey(
        OrderDeliveryStatus, on_delete=models.CASCADE, null=True, blank=True
    )

    assigned_to = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True
    )
    assigned_at = models.DateTimeField(null=True, blank=True)

    customer_company = models.ForeignKey(
        Company, on_delete=models.CASCADE, null=True, blank=True
    )
    breakable = models.BooleanField(
        default=False,
        help_text=_("Whether the order contains breakable items like glass"),
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.customer_name} - {self.created_at}"

    class Meta:
        db_table = "orders"
        verbose_name = _("Order")
        verbose_name_plural = _("Orders")


class OrderAssignmentHistory(models.Model):
    office = models.ForeignKey(Office, on_delete=models.CASCADE)
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    assigned_to = models.ForeignKey(User, on_delete=models.CASCADE)
    assigned_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="assigned_by"
    )
    assigned_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.order.customer_name} - {self.assigned_to.username} - {self.assigned_at}"

    class Meta:
        db_table = "order_assignment_history"
        verbose_name = _("Order Assignment History")
        verbose_name_plural = _("Order Assignment Histories")


class OrderHandlingStatusHistory(models.Model):
    office = models.ForeignKey(Office, on_delete=models.CASCADE)
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    handling_status = models.CharField(
        max_length=20, choices=OrderHandlingStatus.choices, null=True, blank=True
    )
    delivery_status = models.ForeignKey(
        OrderDeliveryStatus, on_delete=models.CASCADE, null=True, blank=True
    )
    changed_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="changed_by"
    )
    note = models.TextField(blank=True)
    proof = models.ForeignKey(
        "OrderProof", on_delete=models.CASCADE, null=True, blank=True
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return (
            f"{self.order.customer_name} - {self.handling_status} - {self.created_at}"
        )

    class Meta:
        db_table = "order_handling_status_history"
        verbose_name = _("Order Handling Status History")
        verbose_name_plural = _("Order Handling Status Histories")


class OrderProofType(models.TextChoices):
    PROOF_OF_ASSIGNMENT = "PROOF_OF_ASSIGNMENT", _("Proof of Assignment")
    PROOF_OF_DELIVERY = "PROOF_OF_DELIVERY", _("Proof of Delivery")
    PROOF_OF_RETURN = "PROOF_OF_RETURN", _("Proof of Return")


class OrderProof(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    proof_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="proof_by"
    )
    proof_type = models.CharField(max_length=50, choices=OrderProofType.choices)
    proof_img = models.ImageField(
        upload_to="uploads/order_proofs/",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)

    def __str__(self):
        return f"{self.order.customer_name} - {self.proof_type}"

    class Meta:
        db_table = "order_proofs"
        verbose_name = _("Order Proof")
        verbose_name_plural = _("Order Proofs")
