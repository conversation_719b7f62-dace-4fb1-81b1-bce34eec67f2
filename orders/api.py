from ninja import File, Form, Router, UploadedFile
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from typing import List, Optional
import tempfile
import os
import logging
from pathlib import Path
from core.schemas import SuccessResponse

logger = logging.getLogger(__name__)
from orders.models import (
    Company,
    CompanyChannel,
    Order,
    OrderDeliveryStatus,
    OrderProof,
    OrderAssignmentHistory,
    OrderHandlingStatusHistory,
    OrderHandlingStatus,
    OrderProofType,
)
from accounts.models import EmployeeBalanceTransaction, TransactionType, User, Role
from .schemas import (
    CompanyInSchema,
    CompanyEditSchema,
    CompanyOutSchema,
    CompanyChannelInSchema,
    CompanyChannelEditSchema,
    CompanyChannelOutSchema,
    OrderExtendedOutSchema,
    OrderInSchema,
    OrderOutSchema,
    OrderAssignSchema,
    OrderOutSchemaWithHistory,
    OrderStatusOutSchema,
    OrderTransferSchema,
    OrderProofInSchema,
    OrderProofOutSchema,
    OrderCompleteSchema,
)
from .excel_import_schemas import ExcelImportRequest, ExcelImportConfig
from .excel_parser_service import parse_excel_orders, parse_excel_orders_intelligent
from .bulk_import_service import bulk_import_orders
from .csv_export_service import (
    export_orders_to_csv_response,
    export_orders_to_csv_arabic,
)
from .export_schemas import (
    SelectiveExportRequest,
    BulkExportRequest,
    apply_filters_to_queryset,
)
from core.errors import HttpError
from core.middleware import AuthBearer

orderApi = Router()
companiesApi = Router()
companyChannelsApi = Router()


# --- Company Endpoints ---
# /companies
@companiesApi.get("/companies", response=List[CompanyOutSchema], auth=AuthBearer())
def list_companies(request):
    user = request.auth
    qs = Company.objects.filter(office=user.office, is_active=True)
    return qs


# /companies
@companiesApi.post("/companies/", response=CompanyOutSchema, auth=AuthBearer())
def add_company(request, payload: CompanyInSchema):
    user = request.auth
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403, "Forbidden: Only Master/Manager can add companies.", "FORBIDDEN"
        )
    try:
        company = Company.objects.create(office=user.office, **payload.dict())
    except Exception as e:
        raise HttpError(400, str(e), "BAD_REQUEST")
    return company


# /companies/{company_id}
@companiesApi.put(
    "/companies/{company_id}/", response=CompanyOutSchema, auth=AuthBearer()
)
def edit_company(request, company_id: int, payload: CompanyEditSchema):
    user = request.auth
    company = get_object_or_404(Company, id=company_id, office=user.office)
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403, "Forbidden: Only Master/Manager can edit companies.", "FORBIDDEN"
        )
    try:
        for attr, value in payload.dict(exclude_unset=True).items():
            setattr(company, attr, value)
        company.save()
    except Exception as e:
        raise HttpError(400, str(e), "BAD_REQUEST")
    return company


# /companies/{company_id}
@companiesApi.delete(
    "/companies/{company_id}/", response=SuccessResponse, auth=AuthBearer()
)
def delete_company(request, company_id: int):
    user = request.auth
    company = get_object_or_404(Company, id=company_id, office=user.office)
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403, "Forbidden: Only Master/Manager can delete companies.", "FORBIDDEN"
        )
    company.is_active = False
    company.save()
    return {"success": True}


# get company by code
@companiesApi.get("/companies/code/", response=CompanyOutSchema, auth=AuthBearer())
def get_company_by_code(request, code: str):
    user = request.auth
    company = get_object_or_404(Company, code=code, office=user.office)
    return company


# --- Order CRUD ---


# /orders/{order_id}
@orderApi.get(
    "/orders/{order_id}/", response=OrderOutSchemaWithHistory, auth=AuthBearer()
)
def get_order(request, order_id: int):
    user = request.auth
    order = get_object_or_404(Order, id=order_id, office=user.office)
    assigning_history = OrderAssignmentHistory.objects.filter(order=order)
    handling_status_history = OrderHandlingStatusHistory.objects.filter(order=order)
    proofs = OrderProof.objects.filter(order=order)
    return {
        "order": order,
        "assigning_history": assigning_history,
        "handling_status_history": handling_status_history,
        "proofs": proofs,
    }


# /orders
@orderApi.get("/orders", response=List[OrderExtendedOutSchema], auth=AuthBearer())
def list_orders(
    request,
    date_from: str = None,
    date_to: str = None,
    status: OrderHandlingStatus = None,
    assigned_to: int = None,
):
    user = request.auth
    qs = Order.objects.filter(office=user.office)
    if date_from:
        qs = qs.filter(created_at__gte=date_from)
    if date_to:
        qs = qs.filter(created_at__lte=date_to)
    if status:
        qs = qs.filter(order_handling_status=status)
    if assigned_to:
        qs = qs.filter(assigned_to_id=assigned_to)
    return qs


# /orders
@orderApi.post("/orders/", response=OrderOutSchema, auth=AuthBearer())
def add_order(request, payload: OrderInSchema):
    user = request.auth
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403, "Forbidden: Only Master/Manager can add orders.", "FORBIDDEN"
        )
    order = Order.objects.create(
        office=user.office,
        code=payload.code,
        notes=payload.notes or "",
        total_price=payload.total_price,
        customer_name=payload.customer_name,
        customer_phone=payload.customer_phone,
        customer_address=payload.customer_address or "",
        customer_company_id=payload.customer_company,
        delivery_deadline_date=payload.delivery_deadline_date,
        order_delivery_status_id=payload.order_delivery_status,
        order_handling_status=OrderHandlingStatus.PENDING,
    )
    return order


# /orders/{order_id}
@orderApi.put("/orders/{order_id}/", response=OrderOutSchema, auth=AuthBearer())
def edit_order(request, order_id: int, payload: OrderInSchema):
    user = request.auth
    order = get_object_or_404(Order, id=order_id, office=user.office)
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403, "Forbidden: Only Master/Manager can edit orders.", "FORBIDDEN"
        )
    with transaction.atomic():
        for attr, value in payload.dict(exclude_unset=True).items():
            if attr == "customer_company":
                company = get_object_or_404(Company, id=value, office=user.office)
                order.customer_company = company
                continue
            if attr == "notes":
                order.notes = value or ""
                continue
            setattr(order, attr, value)
        if payload.order_handling_status:
            order.order_handling_status = payload.order_handling_status
            OrderHandlingStatusHistory.objects.create(
                office=user.office,
                order=order,
                handling_status=payload.order_handling_status,
                changed_by=user,
            )
        order.save()
    return order


# /orders/{order_id}
@orderApi.delete("/orders/{order_id}/", response={"success": bool}, auth=AuthBearer())
def delete_order(request, order_id: int):
    user = request.auth
    order = get_object_or_404(Order, id=order_id, office=user.office)
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403, "Forbidden: Only Master/Manager can delete orders.", "FORBIDDEN"
        )
    order.delete()
    return {"success": True}


# --- Assign Order to Employee ---
# /orders/{order_id}/assign/
@orderApi.post("/orders/{order_id}/assign/", response=OrderOutSchema, auth=AuthBearer())
def assign_order(request, order_id: int, payload: OrderAssignSchema):
    user = request.auth
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403, "Forbidden: Only Master/Manager can assign orders.", "FORBIDDEN"
        )

    with transaction.atomic():
        order = get_object_or_404(Order, id=order_id, office=user.office)
        employee = get_object_or_404(
            User, id=payload.employee_id, office=user.office, role=Role.EMPLOYEE
        )
        rate = payload.special_commission_rate
        if rate == -1:
            rate = None
        if rate is None:
            rate = employee.commission_rate
        order.assigned_to = employee
        order.assigned_at = timezone.now()
        order.order_handling_status = OrderHandlingStatus.ASSIGNED
        order.special_commission_rate = rate
        order.save()
        OrderAssignmentHistory.objects.create(
            office=user.office, order=order, assigned_to=employee, assigned_by=user
        )
        OrderHandlingStatusHistory.objects.create(
            office=user.office,
            order=order,
            handling_status=OrderHandlingStatus.ASSIGNED,
            changed_by=user,
        )
    return order


# --- Transfer Order ---
# /orders/{order_id}/transfer/
@orderApi.post(
    "/orders/{order_id}/transfer/", response=OrderOutSchema, auth=AuthBearer()
)
def transfer_order(request, order_id: int, payload: OrderTransferSchema):
    user = request.auth
    order = get_object_or_404(Order, id=order_id, office=user.office)

    # Must be PROCESSING with proof
    if (
        order.order_handling_status != OrderHandlingStatus.PROCESSING
        and user.role == Role.EMPLOYEE
    ):
        raise HttpError(
            400, "Order must be in PROCESSING status to transfer.", "BAD_REQUEST"
        )

    # Check if order has a proof already
    # proof = OrderProof.objects.filter(
    #     order=order, proof_by=user, proof_type=OrderProofType.PROOF_OF_ASSIGNMENT
    # ).first()

    # if not proof or order.assigned_at > proof.created_at:
    #     raise HttpError(
    #         400,
    #         "You must add a proof of assignment after being assigned this order before transferring.",
    #         "BAD_REQUEST",
    #     )

    with transaction.atomic():
        # Assign to new employee
        new_employee = get_object_or_404(
            User, id=payload.to_employee_id, office=user.office, role=Role.EMPLOYEE
        )
        order.assigned_to = new_employee
        order.assigned_at = timezone.now()
        order.order_handling_status = OrderHandlingStatus.ASSIGNED
        order.save()
        # Log assignment and status history
        OrderAssignmentHistory.objects.create(
            office=user.office, order=order, assigned_to=new_employee, assigned_by=user
        )
        OrderHandlingStatusHistory.objects.create(
            office=user.office,
            order=order,
            handling_status=OrderHandlingStatus.ASSIGNED,
            changed_by=user,
            # proof=proof,
        )
    return order


# --- Add Proof to Order ---
# /orders/{order_id}/proof/
@orderApi.post(
    "/orders/{order_id}/proof/", response=OrderProofOutSchema, auth=AuthBearer()
)
def add_proof(
    request,
    order_id: int,
    payload: Form[OrderProofInSchema],
    img: UploadedFile = File(None),
):
    user = request.auth
    order = get_object_or_404(Order, id=order_id, office=user.office)

    with transaction.atomic():
        proof = OrderProof.objects.create(
            order=order,
            proof_by=user,
            proof_type=payload.proof_type,
            proof_img=img,
            latitude=payload.latitude,
            longitude=payload.longitude,
        )
        # If employee, set status to PROCESSING
        if (
            user.role == Role.EMPLOYEE
            and order.order_handling_status == OrderHandlingStatus.ASSIGNED
        ):
            order.order_handling_status = OrderHandlingStatus.PROCESSING
            order.save()
            OrderHandlingStatusHistory.objects.create(
                office=user.office,
                order=order,
                handling_status=OrderHandlingStatus.PROCESSING,
                changed_by=user,
                proof=proof,
            )
    return proof


# --- Employee Completes Order Handling ---
# /orders/{order_id}/complete/
@orderApi.post(
    "/orders/{order_id}/complete/", response=OrderOutSchema, auth=AuthBearer()
)
def complete_order(request, order_id: int, payload: OrderCompleteSchema):
    user = request.auth
    order = get_object_or_404(Order, id=order_id, office=user.office, assigned_to=user)
    if user.role != Role.EMPLOYEE:
        raise HttpError(
            403, "Forbidden: Only employees can complete orders.", "FORBIDDEN"
        )

    with transaction.atomic():
        # Add proof if provided
        proof = None
        if payload.proof_url or (payload.latitude and payload.longitude):
            proof = OrderProof.objects.create(
                order=order,
                proof_by=user,
                proof_type=OrderProofType.PROOF_OF_DELIVERY,
                proof_img=payload.proof_url,
                latitude=payload.latitude,
                longitude=payload.longitude,
            )
        # Update payment and delivery status
        if payload.delivery_customer_payment is not None:
            order.delivery_customer_payment = payload.delivery_customer_payment
        if payload.order_delivery_status is not None:
            order.order_delivery_status_id = payload.order_delivery_status
        order.order_handling_status = OrderHandlingStatus.COMPLETED
        order.save()
        # Log status history
        OrderHandlingStatusHistory.objects.create(
            office=user.office,
            order=order,
            handling_status=OrderHandlingStatus.COMPLETED,
            changed_by=user,
            proof=proof,
        )
        # Update employee balance
        employee = order.assigned_to
        if (
            payload.delivery_customer_payment
            and float(payload.delivery_customer_payment) > 0
        ):
            added_to_balance = 0
            if payload.delivery_customer_payment > order.special_commission_rate:
                added_to_balance = order.special_commission_rate
            else:
                added_to_balance = payload.delivery_customer_payment
            employee.balance += float(added_to_balance)
            EmployeeBalanceTransaction.objects.create(
                employee=employee,
                amount=added_to_balance,
                transaction_type=TransactionType.COMMISSION,
                balance_before=employee.balance - float(added_to_balance),
                created_by=user,
                order=order,
            )
        employee.save()
    return order


@orderApi.get(
    "/orders_statuses", auth=AuthBearer(), response=List[OrderStatusOutSchema]
)
def list_order_statuses(request, office_id: int):
    # get all order delivery statuses that has no office
    return OrderDeliveryStatus.objects.filter(
        Q(office__in=[None, office_id]) | Q(office__isnull=True)
    )


# Employees accept an assigned order and start processing it
# Allowed only for the employee whom the order is assigned to and when the current status is ASSIGNED
@orderApi.post("/orders/{order_id}/accept/", response=OrderOutSchema, auth=AuthBearer())
def accept_order(request, order_id: int, payload: OrderProofInSchema = None):
    user = request.auth
    if user.role != Role.EMPLOYEE:
        raise HttpError(
            403, "Forbidden: Only employees can accept orders.", "FORBIDDEN"
        )

    # Retrieve the order and ensure it is assigned to the requesting employee
    order = get_object_or_404(Order, id=order_id, office=user.office, assigned_to=user)

    # Validate current order status
    if order.order_handling_status != OrderHandlingStatus.ASSIGNED:
        raise HttpError(
            400,
            "Only orders that are in ASSIGNED status can be accepted.",
            "BAD_REQUEST",
        )

    with transaction.atomic():
        # Create proof (with optional lat/long) if payload is provided
        proof = None
        if payload:
            proof_type = (
                payload.proof_type
                if payload.proof_type
                else OrderProofType.PROOF_OF_ASSIGNMENT
            )
            proof = OrderProof.objects.create(
                order=order,
                proof_by=user,
                proof_type=proof_type,
                latitude=payload.latitude,
                longitude=payload.longitude,
            )

        # Update handling status to PROCESSING
        order.order_handling_status = OrderHandlingStatus.PROCESSING
        order.save()

        # Log status change history with the proof (if any)
        OrderHandlingStatusHistory.objects.create(
            office=user.office,
            order=order,
            handling_status=OrderHandlingStatus.PROCESSING,
            changed_by=user,
            proof=proof,
        )

    return order


# --- CompanyChannel Endpoints ---
# /company-channels
@companyChannelsApi.get(
    "/company-channels", response=List[CompanyChannelOutSchema], auth=AuthBearer()
)
def list_company_channels(request, company_id: Optional[int] = None):
    user = request.auth
    qs = CompanyChannel.objects.filter(office=user.office)
    if company_id:
        qs = qs.filter(Q(company_id=company_id) | Q(company_id__isnull=True))
    return qs


# /company-channels
@companyChannelsApi.post(
    "/company-channels/", response=CompanyChannelOutSchema, auth=AuthBearer()
)
def add_company_channel(request, payload: CompanyChannelInSchema):
    user = request.auth
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403, "Forbidden: Only Master/Manager can add company channels.", "FORBIDDEN"
        )

    # Verify company exists and belongs to user's office
    company = get_object_or_404(Company, id=payload.company_id, office=user.office)

    try:
        company_channel = CompanyChannel.objects.create(
            office=user.office,
            company=company,
            name=payload.name,
            notes=payload.notes or "",
            channel_whatsapp_number=payload.channel_whatsapp_number,
        )
    except Exception as e:
        raise HttpError(400, str(e), "BAD_REQUEST")
    return company_channel


# /company-channels/{channel_id}
@companyChannelsApi.get(
    "/company-channels/{channel_id}/",
    response=CompanyChannelOutSchema,
    auth=AuthBearer(),
)
def get_company_channel(request, channel_id: int):
    user = request.auth
    channel = get_object_or_404(CompanyChannel, id=channel_id, office=user.office)
    return channel


# /company-channels/{channel_id}
@companyChannelsApi.put(
    "/company-channels/{channel_id}/",
    response=CompanyChannelOutSchema,
    auth=AuthBearer(),
)
def edit_company_channel(request, channel_id: int, payload: CompanyChannelEditSchema):
    user = request.auth
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403,
            "Forbidden: Only Master/Manager can edit company channels.",
            "FORBIDDEN",
        )

    channel = get_object_or_404(CompanyChannel, id=channel_id, office=user.office)

    try:
        update_data = payload.dict(exclude_unset=True)

        # If company_id is being updated, verify the new company exists and belongs to user's office
        if "company_id" in update_data:
            company = get_object_or_404(
                Company, id=update_data["company_id"], office=user.office
            )
            update_data["company"] = company
            del update_data["company_id"]

        for attr, value in update_data.items():
            setattr(channel, attr, value)
        channel.save()
    except Exception as e:
        raise HttpError(400, str(e), "BAD_REQUEST")
    return channel


# /company-channels/{channel_id}
@companyChannelsApi.delete(
    "/company-channels/{channel_id}/", response=SuccessResponse, auth=AuthBearer()
)
def delete_company_channel(request, channel_id: int):
    user = request.auth
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403,
            "Forbidden: Only Master/Manager can delete company channels.",
            "FORBIDDEN",
        )

    channel = get_object_or_404(CompanyChannel, id=channel_id, office=user.office)
    channel.delete()
    return {"success": True}


# --- Excel Import Endpoint ---
@orderApi.post("/orders/import-excel", auth=AuthBearer())
def import_orders_from_excel(
    request,
    excel_file: UploadedFile = File(...),
    skip_duplicates: bool = Form(True),
    create_missing_companies: bool = Form(False),
    default_company_id: Optional[int] = Form(None),
    use_intelligent_mapping: bool = Form(True),
):
    """
    Import orders from an Excel file.

    Args:
        excel_file: The Excel file to import
        skip_duplicates: Whether to skip orders with duplicate codes
        create_missing_companies: Whether to create companies that don't exist
        default_company_id: Default company ID for orders without company
        use_intelligent_mapping: Whether to use intelligent column detection (recommended)

    Returns:
        Import results with statistics and any errors
    """
    user = request.auth

    # Check permissions
    if user.role not in [Role.MASTER, Role.MANAGER]:
        raise HttpError(
            403, "Forbidden: Only Master/Manager can import orders.", "FORBIDDEN"
        )

    # Validate file type
    if not excel_file.name.endswith((".xlsx", ".xls")):
        raise HttpError(
            400,
            "Invalid file type. Only Excel files (.xlsx, .xls) are allowed.",
            "BAD_REQUEST",
        )

    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx") as temp_file:
            for chunk in excel_file.chunks():
                temp_file.write(chunk)
            temp_file_path = temp_file.name

        try:
            # Create import configuration
            import_config = ExcelImportConfig()

            # Parse Excel file using intelligent or traditional mapping
            if use_intelligent_mapping:
                import_result, processed_orders = parse_excel_orders_intelligent(
                    temp_file_path, import_config
                )
            else:
                import_result, processed_orders = parse_excel_orders(
                    temp_file_path, import_config
                )

            if not processed_orders:
                return {
                    "success": False,
                    "message": "No valid orders found in the Excel file",
                    "import_result": import_result.model_dump(),
                }

            # Create import request
            import_request = ExcelImportRequest(
                file_name=excel_file.name,
                office_id=user.office.id,
                default_company_id=default_company_id,
                skip_duplicates=skip_duplicates,
                create_missing_companies=create_missing_companies,
            )

            # Bulk import orders
            final_result = bulk_import_orders(
                processed_orders, import_request, user, user.office
            )

            return {
                "success": True,
                "message": f"Import completed: {final_result.successful_imports} orders imported successfully",
                "import_result": final_result.model_dump(),
            }

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        logger.error(f"Error importing Excel file: {str(e)}")
        raise HttpError(500, f"Import failed: {str(e)}", "INTERNAL_SERVER_ERROR")


# --- Export Endpoints ---


@orderApi.post("/orders/export/preview", auth=AuthBearer())
def preview_export(request, payload: BulkExportRequest):
    """
    Preview orders that would be exported with the given filters.

    Args:
        payload: Bulk export request with filters

    Returns:
        Preview information including count and sample orders
    """
    user = request.auth

    # Check permissions
    if user.role not in [Role.MASTER, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpError(
            403, "Forbidden: You don't have permission to preview exports.", "FORBIDDEN"
        )

    try:
        # Start with base queryset
        orders = Order.objects.filter(office=user.office).select_related(
            "customer_company", "assigned_to", "order_delivery_status"
        )

        # Apply filters
        orders = apply_filters_to_queryset(orders, payload.filters)

        # Apply limit if specified
        if payload.limit:
            orders = orders[: payload.limit]

        total_count = orders.count()

        # Get sample orders (first 5)
        sample_orders = []
        for order in orders[:5]:
            sample_orders.append(
                {
                    "id": order.id,
                    "code": order.code,
                    "customer_name": order.customer_name,
                    "customer_phone": order.customer_phone,
                    "company": order.customer_company.name
                    if order.customer_company
                    else "",
                    "total_price": float(order.total_price) if order.total_price else 0,
                    "status": order.order_handling_status,
                    "created_at": order.created_at.isoformat(),
                }
            )

        # Estimate file size (rough calculation for CSV)
        estimated_size_kb = total_count * 0.2  # Rough estimate: 0.2KB per row for CSV

        return {
            "success": True,
            "total_orders": total_count,
            "sample_orders": sample_orders,
            "estimated_file_size_kb": round(estimated_size_kb, 2),
            "filters_applied": payload.filters.model_dump(exclude_none=True),
        }

    except Exception as e:
        raise e
        # logger.error(f"Error previewing export: {str(e)}")
        # raise HttpError(500, f"Preview failed: {str(e)}", "INTERNAL_SERVER_ERROR")


@orderApi.post("/orders/export/selective", auth=AuthBearer())
def export_orders_selective(request, payload: SelectiveExportRequest):
    """
    Export selected orders to CSV file.

    Args:
        payload: Selective export request with order IDs

    Returns:
        CSV file download response
    """
    user = request.auth

    # Check permissions
    if user.role not in [Role.MASTER, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpError(
            403, "Forbidden: You don't have permission to export orders.", "FORBIDDEN"
        )

    try:
        # Get orders by IDs
        orders = Order.objects.filter(
            id__in=payload.order_ids, office=user.office
        ).select_related("customer_company", "assigned_to", "order_delivery_status")

        if not orders.exists():
            raise HttpError(404, "No orders found with the specified IDs.", "NOT_FOUND")

        # Generate filename
        filename = (
            payload.filename
            or f"orders_selective_export_{len(payload.order_ids)}_items.csv"
        )
        if not filename.endswith(".csv"):
            filename = filename.replace(".xlsx", ".csv").replace(".xls", ".csv")
            if not filename.endswith(".csv"):
                filename += ".csv"

        # Export to CSV
        return export_orders_to_csv_response(orders, filename)

    except Exception as e:
        logger.error(f"Error exporting orders to CSV: {str(e)}")
        raise HttpError(500, f"CSV export failed: {str(e)}", "INTERNAL_SERVER_ERROR")


@orderApi.post("/orders/export/bulk", auth=AuthBearer())
def export_orders_bulk(request, payload: BulkExportRequest):
    """
    Export orders with filters to CSV file.

    Args:
        payload: Bulk export request with filters

    Returns:
        CSV file download response
    """
    user = request.auth

    # Check permissions
    if user.role not in [Role.MASTER, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpError(
            403, "Forbidden: You don't have permission to export orders.", "FORBIDDEN"
        )

    try:
        # Start with base queryset
        orders = Order.objects.filter(office=user.office).select_related(
            "customer_company", "assigned_to", "order_delivery_status"
        )

        # Apply filters
        orders = apply_filters_to_queryset(orders, payload.filters)

        # Apply limit if specified
        if payload.limit:
            orders = orders[: payload.limit]

        if not orders.exists():
            raise HttpError(
                404, "No orders found matching the specified filters.", "NOT_FOUND"
            )

        # Generate filename
        filename = payload.filename or f"orders_bulk_export_{orders.count()}_items.csv"
        if not filename.endswith(".csv"):
            filename = filename.replace(".xlsx", ".csv").replace(".xls", ".csv")
            if not filename.endswith(".csv"):
                filename += ".csv"

        # Export to CSV
        return export_orders_to_csv_response(orders, filename)

    except Exception as e:
        logger.error(f"Error exporting orders to CSV: {str(e)}")
        raise HttpError(500, f"CSV export failed: {str(e)}", "INTERNAL_SERVER_ERROR")


@orderApi.post("/orders/export/bulk-arabic", auth=AuthBearer())
def export_orders_bulk_arabic(request, payload: BulkExportRequest):
    """
    Export orders with filters to CSV file with Arabic headers.

    Args:
        payload: Bulk export request with filters

    Returns:
        CSV file download response with Arabic headers
    """
    user = request.auth

    # Check permissions
    if user.role not in [Role.MASTER, Role.MANAGER, Role.EMPLOYEE]:
        raise HttpError(
            403, "Forbidden: You don't have permission to export orders.", "FORBIDDEN"
        )

    try:
        # Start with base queryset
        orders = Order.objects.filter(office=user.office).select_related(
            "customer_company", "assigned_to", "order_delivery_status"
        )

        # Apply filters
        orders = apply_filters_to_queryset(orders, payload.filters)

        # Apply limit if specified
        if payload.limit:
            orders = orders[: payload.limit]

        if not orders.exists():
            raise HttpError(
                404, "No orders found matching the specified filters.", "NOT_FOUND"
            )

        # Generate filename
        filename = (
            payload.filename or f"orders_arabic_export_{orders.count()}_items.csv"
        )
        if not filename.endswith(".csv"):
            filename = filename.replace(".xlsx", ".csv").replace(".xls", ".csv")
            if not filename.endswith(".csv"):
                filename += ".csv"

        # Export to CSV with Arabic headers
        return export_orders_to_csv_arabic(orders, filename)

    except Exception as e:
        logger.error(f"Error exporting orders to CSV: {str(e)}")
        raise HttpError(500, f"CSV export failed: {str(e)}", "INTERNAL_SERVER_ERROR")
