# Generated by Django 5.2.1 on 2025-06-20 09:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('offices', '0002_alter_office_table'),
        ('orders', '0011_company_code'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrdersBulkSheet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('sheet_id', models.CharField(max_length=255)),
                ('sheet_name', models.CharField(max_length=255)),
                ('sheet_file', models.FileField(upload_to='uploads/orders_bulk_sheets/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offices.office')),
            ],
        ),
        migrations.AddField(
            model_name='order',
            name='orders_bulk_sheet',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.ordersbulksheet'),
        ),
    ]
