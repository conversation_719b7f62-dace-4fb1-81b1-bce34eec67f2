# Generated by Django 5.2.1 on 2025-06-20 09:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('offices', '0002_alter_office_table'),
        ('orders', '0011_company_code'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OrdersBulkSheet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Display name for the import', max_length=255)),
                ('original_filename', models.CharField(help_text='Original filename of the uploaded Excel file', max_length=255)),
                ('sheet_file', models.FileField(blank=True, help_text='Stored copy of the imported Excel file', null=True, upload_to='uploads/orders_bulk_sheets/')),
                ('total_rows_processed', models.PositiveIntegerField(default=0, help_text='Total number of rows processed from Excel')),
                ('successful_imports', models.PositiveIntegerField(default=0, help_text='Number of orders successfully imported')),
                ('failed_imports', models.PositiveIntegerField(default=0, help_text='Number of rows that failed to import')),
                ('skipped_rows', models.PositiveIntegerField(default=0, help_text='Number of rows that were skipped')),
                ('import_config', models.JSONField(blank=True, default=dict, help_text='Configuration used for the import (column mapping, etc.)')),
                ('sheet_id', models.CharField(blank=True, default='', max_length=255)),
                ('sheet_name', models.CharField(blank=True, default='', max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('imported_by', models.ForeignKey(help_text='User who performed the import', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offices.office')),
            ],
            options={
                'verbose_name': 'Orders Bulk Sheet',
                'verbose_name_plural': 'Orders Bulk Sheets',
                'db_table': 'orders_bulk_sheets',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='order',
            name='orders_bulk_sheet',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.ordersbulksheet'),
        ),
    ]
