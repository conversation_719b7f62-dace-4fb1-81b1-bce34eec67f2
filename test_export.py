#!/usr/bin/env python3
"""
Test script for CSV export functionality.
Tests the simplified CSV-only export feature.
"""

import sys
import os
from datetime import date

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_csv_export_service():
    """Test the CSV export service directly."""
    print("=== Testing CSV Export Service ===")
    
    try:
        # Import Django and setup
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from orders.models import Order
        from orders.csv_export_service import export_orders_to_csv_response, export_orders_to_csv_arabic
        
        # Get some test orders
        orders = Order.objects.all()[:5]  # Get first 5 orders
        
        if not orders.exists():
            print("❌ No orders found in database for testing")
            return False
        
        print(f"📊 Found {orders.count()} orders for testing")
        
        # Test 1: English headers CSV
        print("\n🔍 Test 1: English headers CSV")
        try:
            response = export_orders_to_csv_response(orders, "test_english.csv")
            
            print(f"✅ English CSV response created")
            print(f"   Content-Type: {response.get('Content-Type')}")
            print(f"   Content-Disposition: {response.get('Content-Disposition')}")
            
            # Get content
            content = response.content.decode('utf-8-sig')
            lines = content.split('\n')[:3]  # First 3 lines
            
            print(f"   Content lines: {len(lines)}")
            for i, line in enumerate(lines):
                if line.strip():
                    print(f"   Line {i+1}: {line.strip()[:100]}...")
                    
        except Exception as e:
            print(f"❌ Error in English CSV: {e}")
            return False
        
        # Test 2: Arabic headers CSV
        print("\n🔍 Test 2: Arabic headers CSV")
        try:
            response = export_orders_to_csv_arabic(orders, "test_arabic.csv")
            
            print(f"✅ Arabic CSV response created")
            print(f"   Content-Type: {response.get('Content-Type')}")
            print(f"   Content-Disposition: {response.get('Content-Disposition')}")
            
            # Get content
            content = response.content.decode('utf-8-sig')
            lines = content.split('\n')[:3]  # First 3 lines
            
            print(f"   Content lines: {len(lines)}")
            for i, line in enumerate(lines):
                if line.strip():
                    print(f"   Line {i+1}: {line.strip()[:100]}...")
                    
        except Exception as e:
            print(f"❌ Error in Arabic CSV: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during CSV export test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_export_api_endpoints():
    """Test the export API endpoints."""
    print("\n=== Testing Export API Endpoints ===")
    
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        
        from django.test import RequestFactory
        from orders.api import export_orders_selective, export_orders_bulk, export_orders_bulk_arabic, preview_export
        from orders.export_schemas import BulkExportRequest, SelectiveExportRequest
        from accounts.models import User
        import json
        
        user = User.objects.first()
        if not user:
            print("❌ No users found")
            return False
        
        print(f"👤 Using user: {user.username}")
        
        factory = RequestFactory()
        
        # Test 1: Selective export
        print("\n🔍 Test 1: Selective export")
        try:
            payload_data = {
                "export_type": "selective",
                "order_ids": [1, 2, 3],
                "filename": "test_selective.csv"
            }
            
            request = factory.post(
                '/orders/export/selective',
                data=json.dumps(payload_data),
                content_type='application/json'
            )
            request.auth = user
            
            payload = SelectiveExportRequest(**payload_data)
            response = export_orders_selective(request, payload)
            
            print(f"✅ Selective export successful")
            print(f"   Content-Type: {response.get('Content-Type')}")
            print(f"   Content-Disposition: {response.get('Content-Disposition')}")
            
        except Exception as e:
            print(f"❌ Error in selective export: {e}")
        
        # Test 2: Bulk export
        print("\n🔍 Test 2: Bulk export")
        try:
            payload_data = {
                "export_type": "bulk",
                "filters": {},
                "filename": "test_bulk.csv",
                "limit": 10
            }
            
            request = factory.post(
                '/orders/export/bulk',
                data=json.dumps(payload_data),
                content_type='application/json'
            )
            request.auth = user
            
            payload = BulkExportRequest(**payload_data)
            response = export_orders_bulk(request, payload)
            
            print(f"✅ Bulk export successful")
            print(f"   Content-Type: {response.get('Content-Type')}")
            print(f"   Content-Disposition: {response.get('Content-Disposition')}")
            
        except Exception as e:
            print(f"❌ Error in bulk export: {e}")
        
        # Test 3: Arabic export
        print("\n🔍 Test 3: Arabic export")
        try:
            payload_data = {
                "export_type": "bulk",
                "filters": {},
                "filename": "test_arabic.csv",
                "limit": 5
            }
            
            request = factory.post(
                '/orders/export/bulk-arabic',
                data=json.dumps(payload_data),
                content_type='application/json'
            )
            request.auth = user
            
            payload = BulkExportRequest(**payload_data)
            response = export_orders_bulk_arabic(request, payload)
            
            print(f"✅ Arabic export successful")
            print(f"   Content-Type: {response.get('Content-Type')}")
            print(f"   Content-Disposition: {response.get('Content-Disposition')}")
            
        except Exception as e:
            print(f"❌ Error in Arabic export: {e}")
        
        # Test 4: Preview
        print("\n🔍 Test 4: Export preview")
        try:
            payload_data = {
                "export_type": "bulk",
                "filters": {},
                "limit": 10
            }
            
            request = factory.post(
                '/orders/export/preview',
                data=json.dumps(payload_data),
                content_type='application/json'
            )
            request.auth = user
            
            payload = BulkExportRequest(**payload_data)
            result = preview_export(request, payload)
            
            print(f"✅ Preview successful")
            print(f"   Total orders: {result['total_orders']}")
            print(f"   Sample orders: {len(result['sample_orders'])}")
            print(f"   Estimated size: {result['estimated_file_size_kb']} KB")
            
        except Exception as e:
            print(f"❌ Error in preview: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing API endpoints: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_export_schemas():
    """Test export schemas and validation."""
    print("\n=== Testing Export Schemas ===")
    
    try:
        from orders.export_schemas import (
            SelectiveExportRequest, BulkExportRequest, OrderExportFilters,
            DateRangeFilter, apply_filters_to_queryset
        )
        from datetime import date, timedelta
        
        # Test selective export request
        print("📋 Testing SelectiveExportRequest...")
        
        selective_data = {
            'export_type': 'selective',
            'order_ids': [1, 2, 3, 4, 5],
            'filename': 'test_selective_export.csv',
            'export_date': date.today()
        }
        
        try:
            selective_request = SelectiveExportRequest(**selective_data)
            print(f"✅ Selective request valid: {len(selective_request.order_ids)} orders")
        except Exception as e:
            print(f"❌ Selective request validation failed: {e}")
            return False
        
        # Test bulk export request
        print("📋 Testing BulkExportRequest...")
        
        # Create date range filter
        date_range = DateRangeFilter(
            start_date=date.today() - timedelta(days=30),
            end_date=date.today()
        )
        
        # Create filters
        filters = OrderExportFilters(
            created_date_range=date_range,
            status=['PENDING', 'COMPLETED'],
            min_total_price=100.0,
            max_total_price=1000.0
        )
        
        bulk_data = {
            'export_type': 'bulk',
            'filters': filters,
            'filename': 'test_bulk_export.csv',
            'limit': 1000
        }
        
        try:
            bulk_request = BulkExportRequest(**bulk_data)
            print(f"✅ Bulk request valid with {len(bulk_request.filters.status or [])} status filters")
        except Exception as e:
            print(f"❌ Bulk request validation failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing schemas: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all export tests."""
    print("🚀 Starting CSV Export Tests\n")
    
    tests = [
        ("Export Schemas", test_export_schemas),
        ("CSV Export Service", test_csv_export_service),
        ("Export API Endpoints", test_export_api_endpoints),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*60}")
    
    # Summary
    print(f"\n🏁 Test Results Summary:")
    print(f"{'='*60}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All CSV export tests passed! The feature is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
